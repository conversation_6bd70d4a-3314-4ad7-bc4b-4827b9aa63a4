networks:
  traefik-local:
    driver: bridge
    name: traefik-local
  internal-local:
    driver: bridge
    name: internal-local

volumes:
  mysql_data_local:
  redis_data_local:
  minio_data_local:
  loki_index_local:
  loki_index_cache_local:
  grafana_data_local:
  traefik_logs_local: {}       # NEW: shared log volume for Traefik access logs
  promtail_positions_local: {} # NEW: positions file for Promtail

services:
  # Traefik Reverse Proxy (Local Testing)
  traefik:
    image: traefik:v3.0
    container_name: traefik_local
    restart: unless-stopped
    command:
      - --api.dashboard=true
      - --api.debug=true
      - --api.insecure=true
      - --log.level=INFO
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --providers.docker.network=traefik-local
      - --entrypoints.web.address=:80
      - --metrics.prometheus=true
      # --- Access log to shared file, in JSON (for Promtail to parse) ---
      - --accesslog=true
      - --accesslog.filepath=/var/log/traefik/access.json
      - --accesslog.format=json
    ports:
      - "80:80"
      - "8080:8080"  # Traefik dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - traefik_logs_local:/var/log/traefik
    networks:
      - traefik-local
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.traefik-dashboard.rule=Host(`traefik.localhost`)"
      - "traefik.http.routers.traefik-dashboard.entrypoints=web"
      - "traefik.http.routers.traefik-dashboard.service=api@internal"

  # MySQL Database
  mysql:
    image: mysql:8.4
    container_name: omo_mysql_local
    restart: unless-stopped
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_NAME}
      MYSQL_USER: ${DB_USER}
      MYSQL_PASSWORD: ${DB_PASSWORD}
    volumes:
      - mysql_data_local:/var/lib/mysql
      - ./omo-shopify-app/init.sql:/docker-entrypoint-initdb.d
    networks:
      - internal-local
    command: >
      --innodb-buffer-pool-size=512M
      --innodb-redo-log-capacity=268435456
      --max-connections=100
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${DB_ROOT_PASSWORD}"]
      timeout: 10s
      retries: 10
      interval: 30s

  # Redis Cache & Queue
  redis:
    image: redis:7-alpine
    container_name: omo_redis_local
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy noeviction
    volumes:
      - redis_data_local:/data
    networks:
      - internal-local
    healthcheck:
      test: ["CMD-SHELL", "redis-cli ping | grep PONG"]
      interval: 10s
      timeout: 3s
      retries: 5

  # MinIO Object Storage
  minio:
    image: quay.io/minio/minio:latest
    container_name: omo_minio_local
    restart: unless-stopped
    command: server /data --console-address ":9001"
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
    volumes:
      - minio_data_local:/data
    networks:
      - internal-local
      - traefik-local
    labels:
      - "traefik.enable=true"
      # MinIO API
      - "traefik.http.routers.minio-api.rule=Host(`storage.localhost`)"
      - "traefik.http.routers.minio-api.entrypoints=web"
      - "traefik.http.routers.minio-api.service=minio-api"
      - "traefik.http.services.minio-api.loadbalancer.server.port=9000"
      # MinIO Console
      - "traefik.http.routers.minio-console.rule=Host(`minio.localhost`)"
      - "traefik.http.routers.minio-console.entrypoints=web"
      - "traefik.http.routers.minio-console.service=minio-console"
      - "traefik.http.services.minio-console.loadbalancer.server.port=9001"

  # MinIO Bucket Initialization
  minio-mc-init:
    image: minio/mc:latest
    depends_on:
      - minio
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
    networks:
      - internal-local
    entrypoint: >
      /bin/sh -c "
      until (mc alias set local http://minio:9000 $$MINIO_ROOT_USER $$MINIO_ROOT_PASSWORD) do sleep 1; done &&
      mc mb -p local/loki || true &&
      mc mb -p local/backups || true
      "
    restart: "no"

  # Loki Volume Initialization
  loki-init:
    image: alpine:latest
    container_name: loki_init_local
    volumes:
      - loki_index_local:/loki/index
      - loki_index_cache_local:/loki/index_cache
    command: >
      sh -c "
      mkdir -p /loki/index/uploader /loki/index_cache &&
      chmod -R 777 /loki/index /loki/index_cache &&
      chown -R 10001:10001 /loki/index /loki/index_cache || true
      "
    restart: "no"

  # Loki Log Aggregation
  loki:
    image: grafana/loki:latest
    container_name: omo_loki_local
    restart: unless-stopped
    depends_on:
      - minio
      - minio-mc-init
      - loki-init
    command: ["-config.file=/etc/loki/loki-config.yml"]
    environment:
      LOKI_S3_ENDPOINT: http://minio:9000
      LOKI_S3_BUCKET: loki
      LOKI_S3_REGION: us-east-1
      AWS_ACCESS_KEY_ID: ${MINIO_ROOT_USER}
      AWS_SECRET_ACCESS_KEY: ${MINIO_ROOT_PASSWORD}
    volumes:
      - loki_index_local:/loki/index
      - loki_index_cache_local:/loki/index_cache
      - ./loki-config.yml:/etc/loki/loki-config.yml:ro
    networks:
      - internal-local
    user: "10001"

  # Promtail: tails Traefik logs and pushes to Loki
  promtail:
    image: grafana/promtail:2.9.8
    container_name: omo_promtail_local
    restart: unless-stopped
    depends_on:
      - loki
      - traefik
    command: ["-config.file=/etc/promtail/promtail-config.yml"]
    volumes:
      - traefik_logs_local:/var/log/traefik:ro
      - ./promtail-config.yml:/etc/promtail/promtail-config.yml:ro
      - promtail_positions_local:/positions
    networks:
      - internal-local
      - traefik-local

  # Grafana Monitoring
  grafana:
    image: grafana/grafana:latest
    container_name: omo_grafana_local
    restart: unless-stopped
    depends_on:
      - loki
    ports:
      - "3002:3000"
    environment:
      GF_SECURITY_ADMIN_USER: ${GRAFANA_USER}
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD}
      GF_SERVER_ROOT_URL: http://monitoring.localhost
      GF_SECURITY_COOKIE_SECURE: "false"
      GF_SECURITY_STRICT_TRANSPORT_SECURITY: "false"
    volumes:
      - grafana_data_local:/var/lib/grafana
      - ./provisioning/datasources/datasource.yml:/etc/grafana/provisioning/datasources/datasource.yml:ro
    networks:
      - internal-local
      - traefik-local
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.grafana.rule=Host(`monitoring.localhost`)"
      - "traefik.http.routers.grafana.entrypoints=web"
      - "traefik.http.services.grafana.loadbalancer.server.port=3000"

  # OMO Shopify App
  omo-shopify-app:
    build:
      context: ./omo-shopify-app
      dockerfile: Dockerfile
    container_name: omo_shopify_app_local
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: production
      DATABASE_URL: mysql://${DB_USER}:${DB_PASSWORD}@mysql:3306/${DB_NAME}
      SHOPIFY_API_KEY: ${SHOPIFY_API_KEY}
      SHOPIFY_API_SECRET: ${SHOPIFY_API_SECRET}
      SHOPIFY_APP_URL: http://app.localhost
      SCOPES: ${SHOPIFY_SCOPES}
      SYSTEM_TOKEN: ${SYSTEM_TOKEN}
      API_VERSION: ${SHOPIFY_API_VERSION}
      PORT: 3000
      REDIS_HOST: redis
      REDIS_PORT: 6379
    volumes:
      - ./omo-shopify-app/logs:/app/logs
    networks:
      - internal-local
      - traefik-local
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.shopify-app.rule=Host(`app.localhost`)"
      - "traefik.http.routers.shopify-app.entrypoints=web"
      - "traefik.http.services.shopify-app.loadbalancer.server.port=3000"
      - "traefik.http.routers.shopify-app.middlewares=local-headers"
      - "traefik.http.middlewares.local-headers.headers.customrequestheaders.X-Forwarded-Proto=http"
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # OMO NestJS Backend
  omo-nest-be:
    build:
      context: ./omo-nest-be
      dockerfile: Dockerfile
    container_name: omo_nest_be_local
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      loki:
        condition: service_started
    ports:
      - "3001:3000"
    environment:
      NODE_ENV: development
      DATABASE_URL: mysql://${DB_USER}:${DB_PASSWORD}@mysql:3306/${DB_NAME}
      REDIS_URL: redis://redis:6379
      PORT: 3000
      DB_HOST: ${DB_HOST}
      DB_PORT: ${DB_PORT}
      DB_USERNAME: ${DB_USER}
      DB_PASSWORD: ${DB_PASSWORD}
      DB_DATABASE: ${DB_NAME}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      LOKI_URL: http://loki:3100
    networks:
      - internal-local
      - traefik-local
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.nest-api.rule=Host(`api.localhost`)"
      - "traefik.http.routers.nest-api.entrypoints=web"
      - "traefik.http.services.nest-api.loadbalancer.server.port=3000"
      - "traefik.http.routers.nest-api.middlewares=api-cors-local"
      - "traefik.http.middlewares.api-cors-local.headers.accesscontrolallowmethods=GET,OPTIONS,PUT,POST,DELETE,PATCH"
      - "traefik.http.middlewares.api-cors-local.headers.accesscontrolalloworiginlist=http://app.localhost,http://localhost:3000"
      - "traefik.http.middlewares.api-cors-local.headers.accesscontrolmaxage=100"
      - "traefik.http.middlewares.api-cors-local.headers.addvaryheader=true"
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
